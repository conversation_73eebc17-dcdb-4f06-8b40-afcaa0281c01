import { defineConfig } from '@soybeanjs/eslint-config';

// 这是一个测试注释，用于验证git提交是否正常工作
export default defineConfig(
  // 启用Vue和UnoCSS规则
  { vue: true, unocss: true },
  {
    // 配置全局变量和类型
    languageOptions: {
      globals: {
        // 声明Api为全局类型，避免ESLint报错
        Api: 'readonly'
      }
    },
    rules: {
      // Vue组件命名规则
      'vue/multi-word-component-names': [
        'warn',
        {
          // 允许单词命名的组件
          ignores: ['index', 'App', 'Register', '[id]', '[url]']
        }
      ],
      // 模板中组件名大小写规则
      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        {
          registeredComponentsOnly: false,
          // 忽略以icon-开头的组件
          ignores: ['/^icon-/']
        }
      ],
      // 禁用Vue自定义事件名必须使用camelCase命名规范
      'vue/custom-event-name-casing': 'off',
      // 关闭UnoCSS属性顺序检查
      'unocss/order-attributify': 'off',
      // 允许使用any类型
      '@typescript-eslint/no-explicit-any': 'off',
      // 允许使用ts注释
      '@typescript-eslint/ban-ts-comment': 'off',
      // 允许非空断言
      '@typescript-eslint/no-non-null-assertion': 'off',
      // 未使用变量警告而非错误
      '@typescript-eslint/no-unused-vars': 'warn',
      // 允许只包含静态属性的类
      '@typescript-eslint/no-extraneous-class': 'off'
    }
  }
);