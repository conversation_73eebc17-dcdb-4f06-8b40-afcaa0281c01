/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'aichat',
    path: '/aichat',
    component: 'layout.base$view.aichat',
    meta: {
      title: 'aichat',
      i18nKey: 'route.aichat'
    }
  },
  {
    name: 'asset',
    path: '/asset',
    component: 'layout.base',
    meta: {
      title: 'asset',
      i18nKey: 'route.asset'
    },
    children: [
      {
        name: 'asset_list',
        path: '/asset/list',
        component: 'view.asset_list',
        meta: {
          title: 'asset_list',
          i18nKey: 'route.asset_list'
        }
      }
    ]
  },
  {
    name: 'devicemanagement',
    path: '/devicemanagement',
    component: 'layout.base',
    meta: {
      title: 'devicemanagement',
      i18nKey: 'route.devicemanagement'
    },
    children: [
      {
        name: 'devicemanagement_device',
        path: '/devicemanagement/device',
        component: 'view.devicemanagement_device',
        meta: {
          title: 'devicemanagement_device',
          i18nKey: 'route.devicemanagement_device'
        }
      },
      {
        name: 'devicemanagement_vehicle-location',
        path: '/devicemanagement/vehicle-location',
        component: 'view.devicemanagement_vehicle-location',
        meta: {
          title: 'devicemanagement_vehicle-location',
          i18nKey: 'route.devicemanagement_vehicle-location'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage'
    },
    children: [
      {
        name: 'manage_gateway',
        path: '/manage/gateway',
        component: 'view.manage_gateway',
        meta: {
          title: 'manage_gateway',
          i18nKey: 'route.manage_gateway'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu'
        }
      },
      {
        name: 'manage_org',
        path: '/manage/org',
        component: 'view.manage_org',
        meta: {
          title: 'manage_org',
          i18nKey: 'route.manage_org'
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role'
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user'
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true
        }
      }
    ]
  }
];
