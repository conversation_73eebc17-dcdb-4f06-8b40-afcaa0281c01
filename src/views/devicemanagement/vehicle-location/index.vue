<script setup lang="tsx">
import { onMounted, onUnmounted, ref } from 'vue';
import { NButton, NCard, NSelect, NSpace, NSpin, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import { fetchGetAllOnlineDeviceLocations } from '@/service/api/location';
import NoPermission from '@/components/common/no-permission.vue';
import LocationMap from './modules/location-map.vue';
import DeviceList from './modules/device-list.vue';
import TrackPlayer from './modules/track-player.vue';

defineOptions({
  name: 'VehicleLocationPage'
});

// 权限控制
const { hasAuth } = useAuth();

// 数据状态
const { bool: loading, setTrue: startLoading, setFalse: stopLoading } = useBoolean();
const deviceLocations = ref<Api.Location.DeviceLocationVO[]>([]);
const selectedDevice = ref<Api.Location.DeviceLocationVO | null>(null);

// 轨迹回放相关
const { bool: trackPlayerVisible, setTrue: showTrackPlayer } = useBoolean();

// 自动刷新相关
const refreshInterval = ref(30); // 默认30秒
const { bool: autoRefreshEnabled } = useBoolean(true);
let refreshTimer: NodeJS.Timeout | null = null;

// 地图视野控制
const isInitialLoad = ref(true); // 是否是初次加载

// 刷新间隔选项
const refreshOptions = [
  { label: '不自动刷新', value: 0 },
  { label: '1秒', value: 1 },
  { label: '10秒', value: 10 },
  { label: '30秒', value: 30 },
  { label: '60秒', value: 60 }
];

// 统计信息
const stats = ref({
  totalDevices: 0,
  onlineDevices: 0,
  lastUpdateTime: ''
});

/** 获取所有在线设备位置 */
async function getAllOnlineDeviceLocations() {
  startLoading();
  try {
    const { data, error } = await fetchGetAllOnlineDeviceLocations();
    if (!error && data) {
      deviceLocations.value = data;
      stats.value = {
        totalDevices: data.length,
        onlineDevices: data.filter(d => d.deviceTime).length,
        lastUpdateTime: new Date().toLocaleString()
      };
      isInitialLoad.value = false; // 标记初次加载完成
    } else {
      // API返回错误，清空数据并显示错误状态
      deviceLocations.value = [];
      stats.value = {
        totalDevices: 0,
        onlineDevices: 0,
        lastUpdateTime: new Date().toLocaleString()
      };
      window.$message?.error('获取设备位置数据失败');
    }
  } catch {
    // API调用异常，清空数据并显示错误状态
    deviceLocations.value = [];
    stats.value = {
      totalDevices: 0,
      onlineDevices: 0,
      lastUpdateTime: new Date().toLocaleString()
    };
    window.$message?.error('网络错误，无法获取设备位置数据');
  } finally {
    stopLoading();
  }
}

/** 静默刷新设备位置（用于自动刷新，不显示加载状态） */
async function silentRefreshDeviceLocations() {
  try {
    const { data, error } = await fetchGetAllOnlineDeviceLocations();
    if (!error && data) {
      deviceLocations.value = data;
      stats.value = {
        totalDevices: data.length,
        onlineDevices: data.filter(d => d.deviceTime).length,
        lastUpdateTime: new Date().toLocaleString()
      };
    } else {
      // API返回错误，静默清空数据
      deviceLocations.value = [];
      stats.value = {
        totalDevices: 0,
        onlineDevices: 0,
        lastUpdateTime: new Date().toLocaleString()
      };
    }
  } catch {
    // API调用异常，静默清空数据
    deviceLocations.value = [];
    stats.value = {
      totalDevices: 0,
      onlineDevices: 0,
      lastUpdateTime: new Date().toLocaleString()
    };
  }
}

/** 选择设备 */
function handleSelectDevice(device: Api.Location.DeviceLocationVO) {
  selectedDevice.value = device;
}

/** 显示设备轨迹 */
function handleShowTrack(device: Api.Location.DeviceLocationVO) {
  // 检查轨迹回放权限
  if (!hasAuth('jt808:vehicle-location:track')) {
    window.$message?.warning('您没有轨迹回放权限');
    return;
  }
  selectedDevice.value = device;
  showTrackPlayer();
}

/** 手动刷新 */
function handleRefresh() {
  // 检查刷新权限
  if (!hasAuth('jt808:vehicle-location:refresh')) {
    window.$message?.warning('您没有刷新权限');
    return;
  }
  getAllOnlineDeviceLocations();
}

/** 获取设备状态类型 */
function getDeviceStatusType(device: Api.Location.DeviceLocationVO) {
  const now = new Date();
  const deviceTime = new Date(device.deviceTime);
  const diffMinutes = (now.getTime() - deviceTime.getTime()) / (1000 * 60);

  if (diffMinutes <= 5) return 'success'; // 在线
  if (diffMinutes <= 30) return 'warning'; // 离线
  return 'error'; // 失联
}

/** 获取设备状态文本 */
function getDeviceStatusText(device: Api.Location.DeviceLocationVO) {
  const now = new Date();
  const deviceTime = new Date(device.deviceTime);
  const diffMinutes = (now.getTime() - deviceTime.getTime()) / (1000 * 60);

  if (diffMinutes <= 5) return '在线';
  if (diffMinutes <= 30) return '离线';
  return '失联';
}

/** 启动自动刷新 */
function startAutoRefresh() {
  stopAutoRefresh();

  // 检查刷新权限
  if (!hasAuth('jt808:vehicle-location:refresh')) {
    return;
  }

  if (refreshInterval.value > 0 && autoRefreshEnabled.value) {
    refreshTimer = setInterval(() => {
      silentRefreshDeviceLocations(); // 使用静默刷新，不显示加载状态
    }, refreshInterval.value * 1000);
  }
}

/** 停止自动刷新 */
function stopAutoRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
}

/** 刷新间隔改变 */
function handleRefreshIntervalChange(value: number) {
  // 检查刷新权限
  if (!hasAuth('jt808:vehicle-location:refresh')) {
    window.$message?.warning('您没有刷新权限');
    return;
  }
  refreshInterval.value = value;
  autoRefreshEnabled.value = value > 0;

  if (value > 0) {
    startAutoRefresh();
    window.$message?.success(`已设置${value}秒自动刷新`);
  } else {
    stopAutoRefresh();
    window.$message?.info('已关闭自动刷新');
  }
}

onMounted(() => {
  getAllOnlineDeviceLocations();
  startAutoRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <div v-if="hasAuth('jt808:vehicle-location:view')" class="vehicle-location-page flex-col-stretch gap-8px overflow-hidden">
    <!-- 顶部统计信息 -->
    <NCard :bordered="false" size="small" class="card-wrapper">
      <div class="flex items-center justify-between">
        <NSpace>
          <div class="flex items-center gap-8px">
            <SvgIcon icon="mdi:car-multiple" class="text-20px text-primary" />
            <span class="text-16px font-600">车辆定位监控</span>
          </div>
          <NTag type="info" size="small">总设备: {{ stats.totalDevices }}</NTag>
          <NTag type="success" size="small">在线: {{ stats.onlineDevices }}</NTag>
          <span class="text-12px text-gray-500">最后更新: {{ stats.lastUpdateTime }}</span>
        </NSpace>
        <NSpace>
          <NButton
            v-if="hasAuth('jt808:vehicle-location:refresh')"
            type="primary"
            size="small"
            :loading="loading"
            @click="handleRefresh"
          >
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            刷新
          </NButton>

          <NSelect
            v-if="hasAuth('jt808:vehicle-location:refresh')"
            v-model:value="refreshInterval"
            :options="refreshOptions"
            size="small"
            class="refresh-select"
            @update:value="handleRefreshIntervalChange"
          />

          <NTag v-if="autoRefreshEnabled" type="success" size="small">
            <template #icon>
              <SvgIcon icon="mdi:autorenew" />
            </template>
            自动刷新
          </NTag>

          <span v-if="stats.lastUpdateTime" class="text-12px text-gray-500">更新: {{ stats.lastUpdateTime }}</span>
        </NSpace>
      </div>
    </NCard>

    <!-- 主要内容区域 -->
    <div class="flex flex-1-hidden gap-8px">
      <!-- 左侧设备列表 -->
      <div class="w-300px flex-col-stretch">
        <DeviceList
          :devices="deviceLocations"
          :selected-device="selectedDevice"
          :loading="loading"
          @select-device="handleSelectDevice"
          @show-track="handleShowTrack"
        />
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content-container flex-1-hidden">
        <!-- 地图区域 - 占用70%的高度 -->
        <div class="map-area">
          <NSpin :show="loading" class="h-full">
            <LocationMap
              :devices="deviceLocations"
              :selected-device="selectedDevice"
              :auto-fit-view="isInitialLoad"
              @select-device="handleSelectDevice"
            />
          </NSpin>
        </div>

        <!-- 车辆信息展示区域 - 占用10%的高度 -->
        <div class="vehicle-info-area mt-8px">
          <NCard :bordered="false" class="h-full card-wrapper" content-class="flex-col h-full">
            <div class="mb-12px flex items-center justify-between">
              <div class="flex items-center gap-8px">
                <SvgIcon icon="mdi:car-info" class="text-16px text-primary" />
                <span class="text-14px font-600">车辆信息</span>
              </div>
              <NSpace v-if="selectedDevice">
                <NTag :type="getDeviceStatusType(selectedDevice)" size="small">
                  {{ getDeviceStatusText(selectedDevice) }}
                </NTag>
                <NButton
                  v-if="hasAuth('jt808:vehicle-location:track')"
                  size="tiny"
                  type="primary"
                  @click="handleShowTrack(selectedDevice)"
                >
                  <template #icon>
                    <SvgIcon icon="mdi:map-marker-path" />
                  </template>
                  轨迹回放
                </NButton>
              </NSpace>
            </div>

            <!-- 车辆详细信息 -->
            <div v-if="selectedDevice" class="flex-1-hidden overflow-y-auto">
              <div class="grid grid-cols-1 gap-16px md:grid-cols-2 xl:grid-cols-3">
                <!-- 基本信息 -->
                <div class="space-y-8px">
                  <div class="mb-8px text-12px text-gray-600 font-600">基本信息</div>
                  <div class="info-item">
                    <span class="label">车牌号:</span>
                    <span class="value">{{ selectedDevice.plateNo || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">设备ID:</span>
                    <span class="value">{{ selectedDevice.deviceId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">手机号:</span>
                    <span class="value">{{ selectedDevice.mobileNo || '-' }}</span>
                  </div>
                </div>

                <!-- 位置信息 -->
                <div class="space-y-8px">
                  <div class="mb-8px text-12px text-gray-600 font-600">位置信息</div>
                  <div class="info-item">
                    <span class="label">经度:</span>
                    <span class="value">{{ selectedDevice.lng?.toFixed(6) || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">纬度:</span>
                    <span class="value">{{ selectedDevice.lat?.toFixed(6) || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">地址:</span>
                    <span class="value truncate" :title="selectedDevice.address">
                      {{ selectedDevice.address || '-' }}
                    </span>
                  </div>
                </div>

                <!-- 状态信息 -->
                <div class="space-y-8px">
                  <div class="mb-8px text-12px text-gray-600 font-600">状态信息</div>
                  <div class="info-item">
                    <span class="label">速度:</span>
                    <span class="value" :class="{ 'text-green-600': selectedDevice.speedKph > 0 }">
                      {{ (selectedDevice.speedKph || 0).toFixed(1) }} km/h
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="label">方向:</span>
                    <span class="value">{{ selectedDevice.direction || 0 }}°</span>
                  </div>
                  <div class="info-item">
                    <span class="label">卫星数:</span>
                    <span
                      class="value"
                      :class="{
                        'text-green-600': selectedDevice.satelliteCount >= 8,
                        'text-yellow-600': selectedDevice.satelliteCount >= 4 && selectedDevice.satelliteCount < 8,
                        'text-red-600': selectedDevice.satelliteCount < 4
                      }"
                    >
                      {{ selectedDevice.satelliteCount || 0 }}
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="label">更新时间:</span>
                    <span class="value text-10px">
                      {{ selectedDevice.deviceTime ? new Date(selectedDevice.deviceTime).toLocaleString() : '-' }}
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="label">海拔:</span>
                    <span class="value">{{ selectedDevice.altitude || 0 }} m</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 未选中车辆时的提示 -->
            <div v-else class="flex flex-1-hidden items-center justify-center text-gray-400">
              <div class="text-center">
                <SvgIcon icon="mdi:car-select" class="mb-8px text-48px" />
                <div class="text-14px">请从左侧列表或地图上选择一辆车辆</div>
              </div>
            </div>
          </NCard>
        </div>
      </div>
    </div>

    <!-- 轨迹回放弹窗 -->
    <TrackPlayer
      v-if="hasAuth('jt808:vehicle-location:track')"
      v-model:visible="trackPlayerVisible"
      :device="selectedDevice"
    />
  </div>

  <!-- 无权限提示 -->
  <NoPermission v-else :description="'您没有访问车辆定位页面的权限'" />
</template>

<style scoped>
.card-wrapper {
  --n-padding-top: 12px;
  --n-padding-bottom: 12px;
  --n-padding-left: 16px;
  --n-padding-right: 16px;
}

/* 刷新选择器宽度 */
.refresh-select {
  width: 120px;
}

/* 右侧内容容器样式 - 替代内联样式 */
.right-content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 车辆信息样式 */
.info-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1.5;
}

.info-item .label {
  color: #666;
  min-width: 60px;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  font-weight: 500;
  margin-left: 8px;
  flex: 1;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 状态颜色 */
.text-green-600 {
  color: #16a34a;
}

.text-yellow-600 {
  color: #ca8a04;
}

.text-red-600 {
  color: #dc2626;
}

/* 车辆信息区域滚动条样式 */
.flex-1-hidden::-webkit-scrollbar {
  width: 4px;
}

.flex-1-hidden::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.flex-1-hidden::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.flex-1-hidden::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自适应布局 */
.map-area {
  flex: 8; /* 地图占99%高度 - 测试用 */
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.vehicle-info-area {
  flex: 2; /* 车辆信息占1%高度 - 测试用 */
  min-height: 0;
}

/* 响应式设计 - 移除最小高度限制，完全使用flex比例 */
@media (max-height: 800px) {
  /* 在小屏幕上保持flex比例 */
}

@media (min-height: 1200px) {
  /* 在大屏幕上保持flex比例 */
}

@media (max-width: 1366px) {
  .vehicle-info-area .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .vehicle-info-area .grid {
    grid-template-columns: 1fr;
  }
}

/* 页面整体高度 - 关键修复 */
.vehicle-location-page {
  height: calc(100vh - 80px); /* 减去顶部导航栏高度 */
  min-height: 600px;
  box-sizing: border-box;
}

/* 确保所有父容器都有高度 */
:deep(.n-layout-content) {
  height: 100%;
}

:deep(.flex-grow) {
  height: 100%;
}

/* 确保NSpin组件能正确传递高度 - 关键修复 */
.map-area :deep(.n-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-area :deep(.n-spin-content) {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.map-area :deep(.n-spin-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 确保车辆信息区域的NCard也能正确传递高度 */
.vehicle-info-area :deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.vehicle-info-area :deep(.n-card__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
</style>
