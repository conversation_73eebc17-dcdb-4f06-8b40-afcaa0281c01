<script setup lang="ts">
import { computed, ref } from 'vue';
import { NButton, NCard, NEmpty, NInput, NSpin, NTag, NTooltip } from 'naive-ui';
import { useAuth } from '@/hooks/business/auth';

defineOptions({
  name: 'DeviceList'
});

interface Props {
  devices: Api.Location.DeviceLocationVO[];
  selectedDevice: Api.Location.DeviceLocationVO | null;
  loading: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'select-device', device: Api.Location.DeviceLocationVO): void;
  (e: 'show-track', device: Api.Location.DeviceLocationVO): void;
}

const emit = defineEmits<Emits>();

// 权限控制
const { hasAuth } = useAuth();

// 搜索关键词
const searchKeyword = ref('');

// 过滤后的设备列表
const filteredDevices = computed(() => {
  if (!searchKeyword.value) {
    return props.devices;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return props.devices.filter(
    device =>
      device.plateNo?.toLowerCase().includes(keyword) ||
      device.mobileNo?.toLowerCase().includes(keyword) ||
      device.deviceId?.toLowerCase().includes(keyword)
  );
});

// NTag组件支持的类型
type TagType = 'primary' | 'info' | 'success' | 'warning' | 'error' | 'default';

// 获取设备状态
function getDeviceStatus(device: Api.Location.DeviceLocationVO): { type: TagType; text: string } {
  const now = new Date();
  const deviceTime = new Date(device.deviceTime);
  const diffMinutes = (now.getTime() - deviceTime.getTime()) / (1000 * 60);

  if (diffMinutes <= 5) {
    return { type: 'success' as TagType, text: '在线' };
  } else if (diffMinutes <= 30) {
    return { type: 'warning' as TagType, text: '离线' };
  }
  return { type: 'error' as TagType, text: '失联' };
}

// 获取速度状态
function getSpeedStatus(speedKph: number): { type: TagType; text: string } {
  if (speedKph <= 0) {
    return { type: 'default' as TagType, text: '静止' };
  } else if (speedKph <= 5) {
    return { type: 'info' as TagType, text: '缓行' };
  } else if (speedKph <= 60) {
    return { type: 'success' as TagType, text: '正常' };
  }
  return { type: 'warning' as TagType, text: '高速' };
}

// 格式化时间
function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 选择设备
function handleSelectDevice(device: Api.Location.DeviceLocationVO) {
  emit('select-device', device);
}

// 显示轨迹
function handleShowTrack(device: Api.Location.DeviceLocationVO) {
  emit('show-track', device);
}

// 判断设备是否被选中
function isSelected(device: Api.Location.DeviceLocationVO) {
  return props.selectedDevice?.deviceId === device.deviceId;
}
</script>

<template>
  <NCard :bordered="false" class="h-full card-wrapper" content-class="flex-col">
    <!-- 搜索框 -->
    <div class="mb-12px">
      <NInput v-model:value="searchKeyword" placeholder="搜索车牌号、设备号..." clearable>
        <template #prefix>
          <SvgIcon icon="mdi:magnify" class="text-gray-400" />
        </template>
      </NInput>
    </div>

    <!-- 设备列表 -->
    <div class="flex-1-hidden overflow-y-auto">
      <NSpin :show="loading">
        <div v-if="filteredDevices.length > 0" class="space-y-8px">
          <div
            v-for="device in filteredDevices"
            :key="device.deviceId"
            class="device-item cursor-pointer border border-gray-200 rounded-6px p-12px transition-all hover:border-primary hover:shadow-sm"
            :class="{ 'border-primary bg-primary-50': isSelected(device) }"
            @click="handleSelectDevice(device)"
          >
            <!-- 设备基本信息 -->
            <div class="mb-8px flex items-center justify-between">
              <div class="flex items-center gap-8px">
                <SvgIcon icon="mdi:car" class="text-16px text-primary" />
                <span class="text-14px font-600">{{ device.plateNo || device.deviceId }}</span>
              </div>
              <NTag :type="getDeviceStatus(device).type" size="small">
                {{ getDeviceStatus(device).text }}
              </NTag>
            </div>

            <!-- 位置和速度信息 -->
            <div class="text-12px text-gray-600 space-y-4px">
              <div class="flex items-center justify-between">
                <span>速度:</span>
                <NTag :type="getSpeedStatus(device.speedKph).type" size="tiny">
                  {{ device.speedKph?.toFixed(1) || 0 }} km/h
                </NTag>
              </div>
              <div class="flex items-center justify-between">
                <span>方向:</span>
                <span>{{ device.direction || 0 }}°</span>
              </div>
              <div class="flex items-center justify-between">
                <span>更新:</span>
                <span>{{ formatTime(device.deviceTime) }}</span>
              </div>
            </div>

            <!-- 地址信息 -->
            <div v-if="device.address" class="mt-8px">
              <NTooltip :show-arrow="false">
                <template #trigger>
                  <div class="truncate text-12px text-gray-500">
                    <SvgIcon icon="mdi:map-marker" class="mr-4px text-10px" />
                    {{ device.address }}
                  </div>
                </template>
                {{ device.address }}
              </NTooltip>
            </div>

            <!-- 操作按钮 -->
            <div v-if="hasAuth('jt808:vehicle-location:track')" class="mt-8px flex gap-8px">
              <NButton type="primary" size="tiny" quaternary @click.stop="handleShowTrack(device)">
                <template #icon>
                  <SvgIcon icon="mdi:map-marker-path" />
                </template>
                轨迹
              </NButton>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <NEmpty v-else description="暂无设备数据" class="mt-60px">
          <template #icon>
            <SvgIcon icon="mdi:car-off" class="text-40px text-gray-400" />
          </template>
        </NEmpty>
      </NSpin>
    </div>
  </NCard>
</template>

<style scoped>
.card-wrapper {
  --n-padding-top: 16px;
  --n-padding-bottom: 16px;
  --n-padding-left: 16px;
  --n-padding-right: 16px;
}

.device-item {
  transition: all 0.2s ease-in-out;
}

.device-item:hover {
  transform: translateY(-1px);
}
</style>
